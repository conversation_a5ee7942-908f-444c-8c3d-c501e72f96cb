# 证书信息说明文档

两个证书文件的完整信息，包括别名、密钥、公钥、MD5和SHA1指纹等信息。

## 证书1 DistributionCer.p12 (Apple iOS分发证书)

### 基本信息
- **文件名** DistributionCer.p12
- **证书类型** PKCS#12格式
- **密钥密码** 08270827
- **证书用途** iOS应用分发签名

### 证书详细信息
- **别名** iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)
- **所有者** UID=7VTCWFDH4R, CN=iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R), OU=7VTCWFDH4R, O=Jiangsu ShangHuTong Information Technology Co., Ltd., C=CN
- **发布者** CN=Apple Worldwide Developer Relations Certification Authority, OU=G3, O=Apple Inc., C=US
- **序列号** 3E2E430A9B8163466EA50B215B9C2A92
- **有效期** 2025年8月28日 - 2026年8月28日
- **签名算法** SHA256withRSA

### 指纹信息 (16进制格式)
- **MD5指纹** D319890EA8DCDC866FC8736D562D6086
- **SHA1指纹** 1C18C2CB8853533253D7859055EEFF1E6B0252F9

### 公钥信息 (16进制格式)
- **公钥算法** RSA 2048位
- **公钥模数** (16进制)
```
00C3D1BCDD854AF3F02106E0AC1C7042718F50FC7CEEC3631E5B0346DF4206034C7FA9E05D4CD23BE11CC7F3F08E5833BF60AB182EDD59CCD05CFA71ACDC31F1423F58F50CD5189016A15281B655FE8402FFCCD27DDD1A9D15677897160D1683F34C2EF46D33EE28A9C57696B01DA9262C2486DCC7FE6CE41235C17CF89C33A7A2164108D8D9ABA2148D9A01DF975880EB510C09D3BD8DADF99C14D3D86B33DFAFDF97BE68BC1E8AF61F1E69055DA3E1E804EE43B1F9B4FDFD902838049B98240BF92F8D32149B7B1B6C8DC69BAE363D67E7029A0E752F4BB3047E8DBB6E25354C664418F0B0986750583499135A9A10AEF9059F0CA576881C81A252849115A0FDBB
```
- **公钥指数** 65537 (0x10001)

### 密钥标识符 (16进制格式)
- **Subject Key Identifier** 82EAC63BF8BB4C47164155C45EC47244D8F261C9

---

## 证书2 xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名** xinningwei_develop_platform.keystore
- **证书类型** PKCS12格式 (Java Keystore)
- **密钥密码** 08270827
- **证书用途** Android应用签名

### 证书详细信息
- **别名** xinningwei_develop_platform
- **所有者** CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
- **发布者** CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN (自签名证书)
- **序列号** DBCC4B3CF2DA416F
- **有效期** 2025年8月27日 - 2125年8月3日 (100年有效期)
- **签名算法** SHA1withRSA (已标记为弱算法)

### 指纹信息 (16进制格式)
- **MD5指纹** 0D97C761C7D36EAF43A371F23AD56241
- **SHA1指纹** B663DCD18EE3374AD92F2D135C130365E302A818

### 公钥信息 (10进制格式)
- **公钥算法** RSA 2048位
- **公钥模数** (10进制)
```
1193219954571210421526955798648223886893314370904203528869263380148875120159829721210637649025549848972728693071397436848263850823315608985155749033935223469564002228379371396974864846499428304353913131037285411990051053237746224698821160540261045121805715761488006207658626864171598108017574411926295788361856268510745648775702808711927482975263534620892894553109951737453920507385326403325503795179782217879148806767820075327067813071157485775216501478687388647744173840102025368859495753474355185021017521463846705944067993750238757215496920092229326003209185804421394000796305782636145478929432185297238648744733
```
- **公钥指数** 65537

### 密钥标识符 (16进制格式)
- **Subject Key Identifier** A3D2F837943B60712F89F826060A4E8DF18E9DEC

---

## 安全注意事项

1. **密钥保护** 两个证书都使用相同的密码 `08270827`，建议在生产环境中使用不同的强密码。

2. **算法安全性**
   - iOS证书使用SHA256withRSA算法，安全性较好
   - Android证书使用SHA1withRSA算法，已被标记为弱算法，建议升级

3. **证书有效期**
   - iOS证书有效期1年，需要定期更新
   - Android证书有效期100年，但建议定期更换以提高安全性

4. **证书用途**
   - iOS证书用于App Store分发
   - Android证书用于应用签名和发布

---

*文档生成时间 2025年8月28日*
*密钥密码 08270827*
