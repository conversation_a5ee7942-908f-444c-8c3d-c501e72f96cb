# 证书信息说明文档

两个证书文件的完整信息，包括别名、密钥、公钥、MD5和SHA1指纹等信息。

## 证书1 DistributionCer.p12 (Apple iOS分发证书)

### 基本信息
- **文件名** DistributionCer.p12
- **证书类型** PKCS#12格式
- **密钥密码** 08270827
- **证书用途** iOS应用分发签名

### 证书详细信息
- **别名** iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)
- **所有者** UID=7VTCWFDH4R, CN=iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R), OU=7VTCWFDH4R, O=Jiangsu ShangHuTong Information Technology Co., Ltd., C=CN
- **发布者** CN=Apple Worldwide Developer Relations Certification Authority, OU=G3, O=Apple Inc., C=US
- **序列号** 3E2E430A9B8163466EA50B215B9C2A92
- **有效期** 2025年8月28日 - 2026年8月28日
- **签名算法** SHA256withRSA

### 指纹信息 (16进制格式)
- **MD5指纹** D319890EA8DCDC866FC8736D562D6086
- **SHA1指纹** 1C18C2CB8853533253D7859055EEFF1E6B0252F9

### 公钥信息 (16进制格式)
- **公钥算法** RSA 2048位
- **公钥模数** (16进制)
```
00C3D1BCDD854AF3F02106E0AC1C7042718F50FC7CEEC3631E5B0346DF4206034C7FA9E05D4CD23BE11CC7F3F08E5833BF60AB182EDD59CCD05CFA71ACDC31F1423F58F50CD5189016A15281B655FE8402FFCCD27DDD1A9D15677897160D1683F34C2EF46D33EE28A9C57696B01DA9262C2486DCC7FE6CE41235C17CF89C33A7A2164108D8D9ABA2148D9A01DF975880EB510C09D3BD8DADF99C14D3D86B33DFAFDF97BE68BC1E8AF61F1E69055DA3E1E804EE43B1F9B4FDFD902838049B98240BF92F8D32149B7B1B6C8DC69BAE363D67E7029A0E752F4BB3047E8DBB6E25354C664418F0B0986750583499135A9A10AEF9059F0CA576881C81A252849115A0FDBB
```
- **公钥指数** 65537 (0x10001)

### 密钥标识符 (16进制格式)
- **Subject Key Identifier** 82EAC63BF8BB4C47164155C45EC47244D8F261C9

---

## 证书2 xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名** xinningwei_develop_platform.keystore
- **证书类型** PKCS12格式 (Java Keystore)
- **密钥密码** 08270827
- **证书用途** Android应用签名

### 证书详细信息
- **别名** xinningwei_develop_platform
- **所有者** CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
- **发布者** CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN (自签名证书)
- **序列号** DBCC4B3CF2DA416F
- **有效期** 2025年8月27日 - 2125年8月3日 (100年有效期)
- **签名算法** SHA1withRSA (已标记为弱算法)

### 指纹信息 (16进制格式)
- **MD5指纹** 0D97C761C7D36EAF43A371F23AD56241
- **SHA1指纹** B663DCD18EE3374AD92F2D135C130365E302A818

### 公钥信息 (16进制格式)
- **公钥算法** RSA 2048位
- **公钥模数** (16进制)
```
00973BE51128E9701DCFA69A700D8F0DFD83C1FC4C19429770F98ED6ABF26CCDEC7ED74C19B7C5340B752FB20BC825EC2D48FD6C071557D84BC391B112FEB39FDC3B129DF9614059D0E809A2A61FEFE839A1B865A09CC1D36092DEB10C0B35D53D54E1E26934C6FD2D02619B06F4A2498CD25E4348751F36877C6B7827BF04B0740364FAA6F90DA4B5FDFD095F97ABDBE0007A8D5490D7099B3AA96A69BF0E6B4CA2AEC43B4096D218D670D1F2ACBA47BE6DB51DD44C36745C4023C1EDEB99D9E43B8EA2E21CC4500B26314323465AE769424EE3A3DF38CBD985FFDD8863488BF86C22F59CEFAC7A4EDC4336BF3C914ADC9E5FF42DE93C4073545617D8B4E4EB1D
```
- **公钥指数** 65537 (0x10001)

### 密钥标识符 (16进制格式)
- **Subject Key Identifier** A3D2F837943B60712F89F826060A4E8DF18E9DEC

---

## 公钥格式说明

### DER编码格式 vs RSA模数
- **完整公钥 (DER编码)** 包含完整的ASN.1结构，包括算法标识符和公钥数据
- **RSA模数** 仅包含RSA公钥的模数部分，不包含算法信息
- **DER编码长度** 294字节 (588个16进制字符)
- **RSA模数长度** 257字节 (514个16进制字符)

### 公钥结构解析
```
30820122 - SEQUENCE (290 bytes)
300D06092A864886F70D010101050003 - RSA算法标识符
82010F00 - BIT STRING (271 bytes)
3082010A - SEQUENCE (266 bytes)
0282010100 - INTEGER (257 bytes, RSA模数)
[RSA模数数据]
0203010001 - INTEGER (3 bytes, RSA指数 65537)
```

---

## 安全注意事项

1. **密钥保护** 两个证书都使用相同的密码 `08270827`，建议在生产环境中使用不同的强密码。

2. **算法安全性**
   - iOS证书使用SHA256withRSA算法，安全性较好
   - Android证书使用SHA1withRSA算法，已被标记为弱算法，建议升级

3. **证书有效期**
   - iOS证书有效期1年，需要定期更新
   - Android证书有效期100年，但建议定期更换以提高安全性

4. **证书用途**
   - iOS证书用于App Store分发
   - Android证书用于应用签名和发布

---

*文档生成时间 2025年8月28日*
*密钥密码 08270827*
