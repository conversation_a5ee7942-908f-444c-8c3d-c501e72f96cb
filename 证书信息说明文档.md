# 证书信息说明文档

本文档详细展示了两个证书文件的完整信息，包括别名、密钥、公钥、MD5和SHA1指纹等信息，所有数据均以16进制格式展示。

## 证书1: DistributionCer.p12 (Apple iOS分发证书)

### 基本信息
- **文件名**: DistributionCer.p12
- **证书类型**: PKCS#12格式
- **密钥密码**: 08270827
- **证书用途**: iOS应用分发签名

### 证书详细信息
- **别名**: iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R)
- **所有者**: UID=7VTCWFDH4R, CN=iPhone Distribution: Jiangsu ShangHuTong Information Technology Co., Ltd. (7VTCWFDH4R), OU=7VTCWFDH4R, O=Jiangsu ShangHuTong Information Technology Co., Ltd., C=CN
- **发布者**: CN=Apple Worldwide Developer Relations Certification Authority, OU=G3, O=Apple Inc., C=US
- **序列号**: 3e:2e:43:0a:9b:81:63:46:6e:a5:0b:21:5b:9c:2a:92
- **有效期**: 2025年8月28日 - 2026年8月28日
- **签名算法**: SHA256withRSA

### 指纹信息 (16进制格式)
- **MD5指纹**: D3:19:89:0E:A8:DC:DC:86:6F:C8:73:6D:56:2D:60:86
- **SHA1指纹**: 1C:18:C2:CB:88:53:53:32:53:D7:85:90:55:EE:FF:E1:6B:02:52:F9

### 公钥信息 (16进制格式)
- **公钥算法**: RSA 2048位
- **公钥模数** (16进制):
```
00:c3:d1:bc:dd:85:4a:f3:f0:21:06:e0:ac:1c:70:
42:71:8f:50:fc:7c:ee:c3:63:1e:5b:03:46:df:42:
06:03:4c:7f:a9:e0:5d:4c:d2:3b:e1:1c:c7:f3:f0:
8e:58:33:bf:60:ab:18:2e:dd:59:cc:d0:5c:fa:71:
ac:dc:31:f1:42:3f:58:f5:0c:d5:18:90:16:a1:52:
81:b6:55:fe:84:02:ff:cc:d2:7d:dd:1a:9d:15:67:
78:97:16:0d:16:83:f3:4c:2e:f4:6d:33:ee:28:a9:
c5:76:96:b0:1d:a9:26:2c:24:86:dc:c7:fe:6c:e4:
12:35:c1:7c:f8:9c:33:a7:a2:16:41:08:d8:d9:ab:
a2:14:8d:9a:01:df:97:58:80:eb:51:0c:09:d3:bd:
8d:ad:f9:9c:14:d3:d8:6b:33:df:af:df:97:be:68:
bc:1e:8a:f6:1f:1e:69:05:5d:a3:e1:e8:04:ee:43:
b1:f9:b4:fd:fd:90:28:38:04:b1:98:24:0b:f9:2f:
8d:32:14:9b:7b:1b:6c:8d:c6:9b:ae:36:3d:67:e7:
02:9a:0e:75:2f:4b:b3:04:7e:8d:bb:6e:25:35:4c:
66:44:18:f0:b0:98:67:50:58:34:99:13:59:a1:0a:
ef:90:59:f0:ca:57:68:81:c8:1a:25:28:49:15:a0:
fd:bb
```
- **公钥指数**: 65537 (0x10001)

### 密钥标识符 (16进制格式)
- **Subject Key Identifier**: 82:EA:C6:3B:F8:BB:4C:47:16:41:55:4C:5E:C4:72:44:D8:F2:61:C9

---

## 证书2: xinningwei_develop_platform.keystore (Android开发证书)

### 基本信息
- **文件名**: xinningwei_develop_platform.keystore
- **证书类型**: PKCS12格式 (Java Keystore)
- **密钥密码**: 08270827
- **证书用途**: Android应用签名

### 证书详细信息
- **别名**: xinningwei_develop_platform
- **所有者**: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN
- **发布者**: CN=QIANG HU, OU=Nanjing xinningwei Network Technology Co. Ltd, O=Nanjing xinningwei Network Technology Co. Ltd, L=NAN JING, ST=JIANG SU, C=CN (自签名证书)
- **序列号**: dbcc4b3cf2da416f
- **有效期**: 2025年8月27日 - 2125年8月3日 (100年有效期)
- **签名算法**: SHA1withRSA (已标记为弱算法)

### 指纹信息 (16进制格式)
- **MD5指纹**: 0D:97:C7:61:C7:D3:6E:AF:43:A3:71:F2:3A:D5:62:41
- **SHA1指纹**: B6:63:DC:D1:8E:E3:37:4A:D9:2F:2D:13:5C:13:03:65:E3:02:A8:18

### 公钥信息 (16进制格式)
- **公钥算法**: RSA 2048位
- **公钥模数** (16进制):
```
00:97:3b:e5:11:28:e9:70:1d:cf:a6:9a:70:0d:8f:
0d:fd:83:c1:fc:4c:19:42:97:70:f9:8e:d6:ab:f2:
6c:cd:ec:7e:d7:4c:19:b7:c5:34:0b:75:2f:b2:0b:
c8:25:ec:2d:48:fd:6c:07:15:57:d8:4b:c3:91:b1:
12:fe:b3:9f:dc:3b:12:9d:f9:61:40:59:d0:e8:09:
a2:a6:1f:ef:e8:39:a1:b8:65:a0:9c:c1:d3:60:92:
de:b1:0c:0b:35:d5:3d:54:e1:e2:69:34:c6:fd:2d:
02:61:9b:06:f4:a2:49:8c:d2:5e:43:48:75:1f:36:
87:7c:6b:78:27:bf:04:b0:74:03:64:fa:a6:f9:0d:
a4:b5:fd:fd:09:5f:97:ab:db:e0:00:7a:8d:54:90:
d7:09:9b:3a:a9:6a:69:bf:0e:6b:4c:a2:ae:c4:3b:
40:96:d2:18:d6:70:d1:f2:ac:ba:47:be:6d:b5:1d:
d4:4c:36:74:5c:40:23:c1:ed:eb:99:d9:e4:3b:8e:
a2:e2:1c:c4:50:0b:26:31:43:23:46:5a:e7:69:42:
4e:e3:a3:df:38:cb:d9:85:ff:dd:88:63:48:8b:f8:
6c:22:f5:9c:ef:ac:7a:4e:dc:43:36:bf:3c:91:4a:
dc:9e:5f:f4:2d:e9:3c:40:73:54:56:17:d8:b4:e4:
eb:1d
```
- **公钥指数**: 65537 (0x10001)

### 密钥标识符 (16进制格式)
- **Subject Key Identifier**: A3:D2:F8:37:94:3B:60:71:2F:89:F8:26:06:0A:4E:8D:F1:8E:9D:EC

---

## 安全注意事项

1. **密钥保护**: 两个证书都使用相同的密码 `08270827`，建议在生产环境中使用不同的强密码。

2. **算法安全性**: 
   - iOS证书使用SHA256withRSA算法，安全性较好
   - Android证书使用SHA1withRSA算法，已被标记为弱算法，建议升级

3. **证书有效期**:
   - iOS证书有效期1年，需要定期更新
   - Android证书有效期100年，但建议定期更换以提高安全性

4. **证书用途**:
   - iOS证书用于App Store分发
   - Android证书用于应用签名和发布

---

*文档生成时间: 2025年8月28日*
*密钥密码: 08270827*
